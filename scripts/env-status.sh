#!/bin/bash

# Environment Status Display Script
# Shows current environment with color coding

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get current NODE_ENV
CURRENT_ENV=${NODE_ENV:-"not set"}

# Get current git branch
CURRENT_BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")

# Get backend process status
BACKEND_STATUS="stopped"
if pgrep -f "tsx.*src/index.ts" > /dev/null; then
    BACKEND_STATUS="running"
fi

# Display status with colors
echo ""
echo "🔧 Environment Status"
echo "===================="

# Environment indicator
case $CURRENT_ENV in
    "development")
        echo -e "Environment: ${GREEN}●${NC} ${GREEN}DEVELOPMENT${NC}"
        ;;
    "production")
        echo -e "Environment: ${RED}●${NC} ${RED}PRODUCTION${NC}"
        ;;
    *)
        echo -e "Environment: ${YELLOW}●${NC} ${YELLOW}${CURRENT_ENV}${NC}"
        ;;
esac

# Branch indicator
echo -e "Git Branch:  ${BLUE}${CURRENT_BRANCH}${NC}"

# Backend status
if [ "$BACKEND_STATUS" = "running" ]; then
    echo -e "Backend:     ${GREEN}●${NC} ${GREEN}RUNNING${NC}"
else
    echo -e "Backend:     ${YELLOW}●${NC} ${YELLOW}STOPPED${NC}"
fi

echo ""
echo "💡 Quick Commands:"
echo "   npm run dev          # Start development"
echo "   npm run prod:api     # Start production"
echo "   ./scripts/env-status.sh  # Show this status"
echo ""
