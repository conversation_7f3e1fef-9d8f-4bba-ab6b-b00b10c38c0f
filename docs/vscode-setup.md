# VS Code Development Setup

This document explains how to set up VS Code with convenient status bar buttons for managing the development servers.

## 🎯 Quick Setup

### Step 1: Install Required Extension
1. Open VS Code
2. Install the **Action Buttons** extension by `se<PERSON><PERSON><PERSON><PERSON>`
   - Go to Extensions (Ctrl+Shift+X)
   - Search for "Action Buttons"
   - Install "VsCode Action Buttons" by se<PERSON><PERSON><PERSON>e

### Step 2: Open Workspace
- Open the `pop.code-workspace` file in VS Code
- VS Code will automatically suggest installing the Action Buttons extension if not already installed

## 🚀 Status Bar Buttons

Once set up, you'll see these buttons in the VS Code status bar (bottom):

```
[🚀 Start Dev] [🔴 Start Prod] [🛑 Stop Servers] [📊 Env Status] [🔄 Restart]
    Green        Dark Red         Red            Purple        Orange
```

### 🚀 Start Dev (Green)
- **Command**: `npm run dev`
- **Environment**: Development mode
- **Function**: Starts both frontend (5173) and backend (3000) in development
- **Database**: Connects to `peptide_portal_dev`
- **Features**: Hot reload, debug logging

### 🔴 Start Prod (Dark Red)
- **Command**: `npm run prod:api`
- **Environment**: Production mode
- **Function**: Starts backend (3001) in production mode
- **Database**: Connects to `peptide_portal_prod`
- **Features**: Production optimizations, error logging

### 🛑 Stop Servers (Red)
- **Command**: `npm run dev:stop`
- **Function**: Stops all running servers
- **Use**: Clean shutdown of all services

### � Env Status (Purple)
- **Command**: `npm run status`
- **Function**: Shows current environment with color indicators
- **Display**: Environment (🟢 Dev / 🔴 Prod), Git branch, server status
- **Use**: Quick environment verification

### � Restart (Orange)
- **Command**: `npm run dev:stop && npm run dev`
- **Function**: Clean restart of development servers
- **Use**: After configuration changes or when servers are stuck

## 🎨 Button Features

- **Color-coded**: Easy visual identification with semantic colors
- **Theme Compatible**: Colors optimized for both Solarized Light and Dracula themes
- **High Contrast**: WCAG-compliant contrast ratios for accessibility
- **Tooltips**: Hover for detailed descriptions
- **Single Instance**: Prevents multiple executions
- **Terminal Integration**: Commands run in VS Code terminal
- **Workspace Scoped**: Only active when this project is open

## 🔧 Alternative Setup Methods

### Method 1: Workspace File (Recommended)
- Configuration is in `pop.code-workspace`
- Automatically loads when opening the workspace
- Shared across team members

### Method 2: Local Settings
- Configuration is in `.vscode/settings.json`
- Works when opening the folder directly
- Local to your machine

## 🛠️ Customization

To modify the buttons, edit either:
- `pop.code-workspace` (workspace method)
- `.vscode/settings.json` (folder method)

### Button Configuration Options:
```json
{
  "name": "Button Text",
  "color": "#hexcolor",
  "command": "shell command",
  "tooltip": "Description",
  "singleInstance": true,
  "cwd": "${workspaceFolder}"
}
```

### Available Colors (Theme Compatible):
- Green: `#22c55e` (development/safe) - Works in both light and dark themes
- Dark Red: `#dc2626` (production/caution) - High contrast warning color
- Red: `#ef4444` (stop/danger) - High contrast in Solarized Light and Dracula
- Purple: `#8b5cf6` (status/info) - Distinctive color for status display
- Orange: `#f97316` (restart/warning) - Better visibility than yellow in both themes

## 🎨 Environment Visual Indicators

### Status Bar Button Colors
- **🚀 Start Dev** (Green): Safe development environment
- **🔴 Start Prod** (Dark Red): Production environment - use with caution
- **📊 Env Status** (Purple): Shows current environment state

### Terminal Status Display
When you click "📊 Env Status", you'll see:
- **🟢 ● DEVELOPMENT** - Safe to develop and test
- **🔴 ● PRODUCTION** - Live environment, be careful
- **🟡 ● NOT SET** - Environment not configured

### Safety Features
- **Color Psychology**: Green = go/safe, Red = stop/caution
- **Visual Confirmation**: Always shows which environment is active
- **Database Protection**: Prevents accidental production database access
- **Port Separation**: Different ports for different environments

## 🚨 Troubleshooting

### Buttons Not Showing
1. Ensure Action Buttons extension is installed and enabled
2. Reload VS Code window (Ctrl+Shift+P → "Developer: Reload Window")
3. Check if workspace is properly opened

### Commands Not Working
1. Ensure you're in the correct directory
2. Check that npm scripts exist in package.json
3. PM2 will be automatically installed globally when you first run `npm run dev`

### Extension Not Found
- Extension ID: `seunlanlege.action-buttons`
- Marketplace: [Action Buttons](https://marketplace.visualstudio.com/items?itemName=seunlanlege.action-buttons)

## 📝 Development Workflow

### Daily Development
1. **Start Development**: Click 🚀 Start Dev (green button)
2. **Code**: Hot reload handles changes automatically
3. **Check Environment**: Click 📊 Env Status to verify you're in the right environment
4. **Restart if needed**: Click 🔄 Restart if servers get stuck
5. **End of Day**: Click 🛑 Stop Servers (or leave running)

### Testing Production Mode
1. **Stop Development**: Click 🛑 Stop Servers
2. **Start Production**: Click 🔴 Start Prod (dark red button)
3. **Verify Environment**: Click 📊 Env Status (should show 🔴 PRODUCTION)
4. **Test**: Production backend runs on port 3001
5. **Return to Dev**: Click 🛑 Stop Servers, then 🚀 Start Dev

### Environment Safety
- **Visual Confirmation**: Always check 📊 Env Status before making changes
- **Color Coding**: Green = Safe Development, Red = Production (be careful!)
- **Database Protection**: System prevents wrong database connections
- **Port Separation**: Dev (3000) vs Prod (3001) ports

## 🎯 Benefits

- **Environment Safety**: Clear visual distinction between dev and production
- **One-Click Toggle**: Switch between environments with single button clicks
- **Visual Status**: Color-coded environment indicators (🟢 Dev / 🔴 Prod)
- **Database Protection**: Automatic prevention of cross-environment database access
- **No Command Memory**: GUI buttons replace complex terminal commands
- **Persistent Servers**: PM2 keeps servers running between sessions
- **Hot Reload**: Automatic updates on file changes in development
- **Team Consistency**: Shared configuration across all developers
